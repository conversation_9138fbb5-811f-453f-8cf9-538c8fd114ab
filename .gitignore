# Managed by Habitat
/platform/android/gradle/wrapper/gradle-6.7.1-all.zip
/build
/buildtools
/third_party/quickjs/src
/third_party/libcxx
/third_party/v8
/third_party/libcxxabi
/third_party/llvm
/third_party/gyp
/third_party/googletest
/third_party/checkstyle
/third_party/xhook
/third_party/zlib
/third_party/RTF/
/third_party/NativeScript/include
/third_party/NativeScript/lib
*.profraw
__pycache__
/venv
.venv
.venv.lock
*.log*
npm-debug.log*
yarn-debug.log*
yarn-error.log*

/*.podspec
.bundle
.pnpm-debug.log
ruby

# Auto generated files
/platform/android/lynx_android/src/main/java/com/lynx/tasm/featurecount/LynxFeatureCounter.java
/platform/darwin/common/lynx/feature_count/LynxFeature.h
/core/services/feature_count/feature.h
/js_libraries/lynx-core/common/feature.ts
core/build/gen/
/core/build/jni/gen
/core/build/jni/BUILD.gn
**/build/gen


# Auto generated error code files
/platform/darwin/common/lynx/LynxErrorBehavior.m
/platform/darwin/common/lynx/LynxSubErrorCode.m
/platform/darwin/common/lynx/Public/LynxErrorBehavior.h
/platform/darwin/common/lynx/Public/LynxSubErrorCode.h


out/
platform/darwin/ios/JSAssets/
platform/android/lynx_js_sdk/src/debug/assets/
platform/android/lynx_js_sdk/src/main/assets/
tools/android_tools/ndk
tools/android_tools/sdk


platform/android/.gradle/
platform/android/local.properties
platform/android/lynx_android/.cxx/
platform/android/lynx_processor/bin/
platform/android/lynx_android/CMakeLists_impl/
platform/android/lynx_android/libs/
explorer/android/lynx_explorer/build/
explorer/android/.gradle/
explorer/android/.idea/
explorer/android/build/
explorer/android/gradle/wrapper/gradle-6.7.1-all.zip
explorer/android/local.properties
explorer/android/lynx_explorer/src/main/assets/*
!explorer/android/lynx_explorer/src/main/assets/testBench/
core/runtime/jsi/v8/CMakeLists_impl/

explorer/darwin/ios/lynx_explorer/Podfile.lock
explorer/darwin/ios/lynx_explorer/Pods
explorer/darwin/ios/lynx_explorer/LynxExplorer.xcworkspace/*
explorer/darwin/ios/lynx_explorer/LynxExplorer.xcodeproj/**
!explorer/darwin/ios/lynx_explorer/LynxExplorer.xcodeproj/
!explorer/darwin/ios/lynx_explorer/LynxExplorer.xcodeproj/project.pbxproj
!explorer/darwin/ios/lynx_explorer/LynxExplorer.xcodeproj/xcshareddata/
!explorer/darwin/ios/lynx_explorer/LynxExplorer.xcodeproj/xcshareddata/**
explorer/darwin/ios/lynx_explorer/LynxExplorer/Resource/*
!explorer/darwin/ios/lynx_explorer/LynxExplorer/Resource/testBench
explorer/darwin/ios/lynx_explorer/iOSCoreBuild/
explorer/darwin/ios/lynx_explorer/results_bundle
explorer/darwin/ios/lynx_explorer/xctestrunner
ruby/

# JSLibrary
node_modules
js_libraries/**/dist/
js_libraries/**/__dist__
js_libraries/**/output/
js_libraries/**/node_modules/
js_libraries/**/lib
js_libraries/lynx-core/src/common/feature.ts

/core/renderer/css/auto_gen_css_decoder.h
/core/renderer/css/auto_gen_css_decoder.cc
/core/renderer/css/css_property_id.h
/core/renderer/css/css_property_auto_gen_unittest.cc
/core/renderer/css/parser/animation_property_handler.h
/core/renderer/css/parser/bool_handler.h
/core/renderer/css/parser/border_style_handler.h
/core/renderer/css/parser/border_width_handler.h
/core/renderer/css/parser/color_handler.h
/core/renderer/css/parser/length_handler.h
/core/renderer/css/parser/number_handler.h
/core/renderer/css/parser/string_handler.h
/core/renderer/css/parser/time_handler.h
/core/renderer/css/parser/timing_function_handler.h
/core/renderer/starlight/style/auto_gen_css_type.h
/platform/darwin/common/lynx/public/base/LynxAutoGenCSSType.h
/platform/android/lynx_processor/src/main/java/com/lynx/tasm/behavior/PropertyIDConstants.java
/platform/android/lynx_android/src/main/java/com/lynx/tasm/behavior/AutoGenStyleConstants.java

# ignore all python script cache
**/__pycache__/

# ignore os specific hidden files
**/.DS_Store

# Editor specific files
.vscode
*.sublime-project
*.sublime-workspace
.idea

# ignore Gradle files
**/.gradle/

# api metadata
/tools/api/android
/tools/api/ios
