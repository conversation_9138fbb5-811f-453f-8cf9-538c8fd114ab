// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "base/include/fml/hash_combine.h"
#include "third_party/googletest/googletest/include/gtest/gtest.h"

namespace fml {
namespace testing {

TEST(HashCombineTest, CanHash) {
  ASSERT_EQ(HashCombine(), HashCombine());
  ASSERT_EQ(HashCombine("Hello"), <PERSON><PERSON><PERSON><PERSON><PERSON>("Hello"));
  ASSERT_NE(HashCombine("Hello"), HashCombine("World"));
  ASSERT_EQ(HashCombine("Hello", "World"), <PERSON><PERSON><PERSON><PERSON><PERSON>("Hello", "World"));
  ASSERT_NE(Hash<PERSON>ombine("World", "Hello"), <PERSON>h<PERSON><PERSON><PERSON>("Hello", "World"));
  ASSERT_EQ(HashCombine(12u), HashCombine(12u));
  ASSERT_NE(HashCombine(12u), <PERSON><PERSON><PERSON><PERSON><PERSON>(12.0f));
  ASSERT_EQ(<PERSON>h<PERSON><PERSON><PERSON>('a'), <PERSON><PERSON><PERSON><PERSON><PERSON>('a'));
}

}  // namespace testing
}  // namespace fml
