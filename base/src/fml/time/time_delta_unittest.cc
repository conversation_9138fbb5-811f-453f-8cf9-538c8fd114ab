// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
// Copyright 2022 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "base/include/fml/time/time_delta.h"

#include "third_party/googletest/googletest/include/gtest/gtest.h"

namespace lynx {
namespace fml {
namespace {

TEST(TimeDelta, Control) {
  EXPECT_LT(TimeDelta::Min(), TimeDelta::Zero());
  EXPECT_GT(TimeDelta::Max(), TimeDelta::Zero());

  EXPECT_GT(TimeDelta::Zero(), TimeDelta::FromMilliseconds(-100));
  EXPECT_LT(TimeDelta::Zero(), TimeDelta::FromMilliseconds(100));

  EXPECT_EQ(TimeDelta::FromMilliseconds(1000), TimeDelta::FromSeconds(1));
}

}  // namespace
}  // namespace fml
}  // namespace lynx
