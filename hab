#!/usr/bin/env bash

set -e

##############################################################################
##
##  habitat start up script for UN*X
##
##############################################################################
__version__="latest"

if [[ "$HABITAT_DEBUG" = "true" ]]; then
  set -x
fi

debug () {
  if [[ "$HABITAT_DEBUG" = "true" ]]; then
    echo "$*"
  fi
}

info () {
  echo "$*"
}

warn () {
  echo "$*"
}

die () {
  echo
  echo "$*"
  echo
  exit 1
}

HABITAT_VERSION=${HABITAT_VERSION:-$__version__}

HABITAT_RELEASES_BASE_URL=https://github.com/lynx-family/habitat/releases

if [[ $HABITAT_VERSION = 'latest' ]]; then
  HABITAT_DOWNLOAD_URL=$HABITAT_RELEASES_BASE_URL/latest/download/hab.pex
  HABITAT_VERSION=$(curl -I -sL -o /dev/null -w '%{url_effective}' $HABITAT_RELEASES_BASE_URL/latest | grep -oE '[^/]+$')
  debug "Using latest version"
else
  HABITAT_DOWNLOAD_URL=$HABITAT_RELEASES_BASE_URL/download/$HABITAT_VERSION/hab.pex
fi

# Attempt to set APP_HOME
# Resolve links: $0 may be a link
PRG="$0"
# Need this for relative symlinks.
while [ -h "$PRG" ] ; do
    ls=`ls -ld "$PRG"`
    link=`expr "$ls" : '.*-> \(.*\)$'`
    if expr "$link" : '/.*' > /dev/null; then
        PRG="$link"
    else
        PRG=`dirname "$PRG"`"/$link"
    fi
done
SAVED="`pwd`"
cd "`dirname \"$PRG\"`/" >/dev/null
APP_HOME="`pwd -P`"
cd "$SAVED" >/dev/null

# Use the maximum available, or set MAX_FD != -1 to use that value.
MAX_FD="maximum"

# OS specific support (must be 'true' or 'false').
cygwin=false
msys=false
darwin=false
nonstop=false
case "`uname`" in
  CYGWIN* )
    cygwin=true
    ;;
  Darwin* )
    darwin=true
    ;;
  MINGW* )
    msys=true
    ;;
  NONSTOP* )
    nonstop=true
    ;;
esac

# Increase the maximum file descriptors if we can.
if [ "$cygwin" = "false" -a "$nonstop" = "false" ] ; then
    MAX_FD_LIMIT=`ulimit -H -n`
    if [ $? -eq 0 ] ; then
        if [ "$MAX_FD" = "maximum" -o "$MAX_FD" = "max" ] ; then
            MAX_FD="$MAX_FD_LIMIT"
        fi
        ulimit -n $MAX_FD
        if [ $? -ne 0 ] ; then
            warn "Could not set maximum file descriptor limit: $MAX_FD"
        fi
    else
        warn "Could not query maximum file descriptor limit: $MAX_FD_LIMIT"
    fi
fi

HABITAT_CACHE_DIR=$HOME/.habitat/bin
HABITAT_BIN=$HABITAT_CACHE_DIR/hab-$HABITAT_VERSION

install() {
  if [[ ! -d "$HABITAT_CACHE_DIR" ]]; then
    # shellcheck disable=SC2086
    mkdir -p $HABITAT_CACHE_DIR
  fi
  if [[ ! -f "$HABITAT_BIN" ]]; then
    info "Installing habitat ($HABITAT_VERSION) from url $HABITAT_DOWNLOAD_URL..."
    curl -sL "$HABITAT_DOWNLOAD_URL" -o "$HABITAT_BIN"
    chmod +x "$HABITAT_BIN"
  fi
}

install

CURRENT_VERSION=$($HABITAT_BIN -v 2>/dev/null)

for arg in "$@"
do
  if [ -d "$arg" ]; then
    root_dir=$arg
  fi
  if [[ "$arg" == "sync" ]]; then
    is_sync_command=true
  fi
done

exit_code=0
if [ $is_sync_command ]; then
  err_msg=$(HABITAT_BIN deps "$root_dir" 2>&1 1>/dev/null) || exit_code=$?
  if [[ $exit_code = 126 ]]; then
    # 126 means incompatible version
    PATTERN="([0-9]+\.[0-9]+\.[0-9]{1,3}(-[a-z]+\.[0-9]+)?)"
    HABITAT_VERSION=$(echo "$err_msg" | grep -E "^expected\ version:\ " | grep -E "$PATTERN" -o)
    debug "required version ${HABITAT_VERSION} does not exist, try installing it first (current version is $CURRENT_VERSION)"
    install
  fi
fi

exit_code=0
debug Using habitat "$HABITAT_VERSION"
$HABITAT_BIN "$@" || exit_code=$?

exit $exit_code
