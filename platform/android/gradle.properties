# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Settings specified in this file will override any Gradle settings
# configured through the IDE.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# The Gradle daemon aims to improve the startup and execution time of Gradle.
# When set to true the Gradle daemon is to run the build.
# TODO: disable daemon on CI, since builds should be clean and reliable on servers
#org.gradle.daemon=true

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx10248m -XX:MaxPermSize=256m
org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#org.gradle.jvmargs=-Xdebug -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5015

# use for debug apt.
#org.gradle.jvmargs=-Xmx4096m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005
#org.gradle.daemon=true
# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true

# Enables new incubating mode that makes Gradle selective when configuring projects.
# Only relevant projects are configured which results in faster builds for large multi-projects.
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:configuration_on_demand
org.gradle.configureondemand=false

# AndroidX:
android.useAndroidX=false
android.enableJetifier=false

# C++ build:
CMAKE_VERSION=3.18.1
ndk_version=21.4.7075529
enable_lto=true
enable_custom_lld=false
compiler_optimization_level=O3

# Feature control:
# The product package is of a reduced volume, being appropriate for the scenarios 
# which are more acutely sensitive to the aspects of package size and performance
enable_lite=false
# Even lighter. Remove all logs and inspector in production.
enable_lite_production=false

# Testing:
enable_lynx_android_test=false
enable_coverage=false
enable_cpp_coverage=false

VERSION = 0.0.1

# Used to specify the architecture types for building native code. The optional values are: armeabi-v7a, x86, x86_64, arm64-v8a.
# When setting this value, please use commas to separate different architecture types. For example: abiList=armeabi-v7a,x86
abiList=arm64-v8a
