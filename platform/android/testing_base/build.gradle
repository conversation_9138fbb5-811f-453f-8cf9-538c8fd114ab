// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
apply plugin: 'com.android.library'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 22

        consumerProguardFiles "consumer-rules.pro"
        missingDimensionStrategy 'lynx','noasan'
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {
            debuggable true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    compileOnly project(':LynxAndroid')
    implementation 'com.android.support:support-annotations:28.0.0'
    api 'org.mockito:mockito-android:4.8.1'
    
    // JUnit dependencies
    implementation 'junit:junit:4.12'
    
    // Android testing dependencies
    implementation 'com.android.support.test:runner:1.0.2'
    implementation 'com.android.support.test:rules:1.0.2'
    implementation 'com.android.support.test.espresso:espresso-core:3.0.2'
    
    // Fresco dependencies
    implementation 'com.facebook.fresco:fresco:1.10.0'
    implementation 'com.facebook.fresco:animated-base:1.10.0'
    implementation 'com.facebook.fresco:imagepipeline-okhttp3:1.10.0'
    implementation 'com.facebook.fresco:fbcore:1.10.0'
    implementation 'com.facebook.fresco:imagepipeline:1.10.0'
    implementation 'com.facebook.fresco:drawee:1.10.0'
}
