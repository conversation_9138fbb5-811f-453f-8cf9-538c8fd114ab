// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#include "quickjs/include/primjs_monitor.h"

void MonitorEvent(const char*, const char*, const char*, const char*) {}

int GetSettingsFlag() { return 0; }
bool GetSettingsWithKey(const char* key) { return false; }
