// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.

#ifndef THIRD_PARTY_GEN_TEST_NAPI_GEN_TEST_COMMAND_BUFFER_H_
#define THIRD_PARTY_GEN_TEST_NAPI_GEN_TEST_COMMAND_BUFFER_H_

#include <map>

#include "base/include/log/logging.h"
#include "third_party/binding/napi/napi_bridge.h"
#include "base/include/auto_reset.h"
#include "base/include/shared_vector.h"

namespace lynx {
namespace gen_test {

class NapiAsyncObject;
class VSyncSource;

using binding::NapiBridge;
using binding::ImplBase;
using base::AutoReset;
using base::SharedVector;

class NapiGenTestCommandBuffer : public Napi::ScriptWrappable {
 public:
  explicit NapiGenTestCommandBuffer(const Napi::CallbackInfo&);

  static Napi::Value GetCommandBufferInstance(const Napi::CallbackInfo&);
  static void Install(Napi::Env, Napi::Object&);

  Napi::Value Call(const Napi::CallbackInfo&);

  static void FlushCommandBuffer(Napi::Env, bool = false);
  Napi::Value Flush(const Napi::CallbackInfo&);

  // Register an object and its type to command buffer. Return its id.
  static uint32_t RegisterBufferedObject(
      Napi::ScriptWrappable* wrapped);
  static void UnregisterBufferedObject(
      Napi::ScriptWrappable* wrapped, uint32_t id);

  static void RegisterAsyncObject(NapiAsyncObject* async_object);
  static void UnregisterAsyncObject(NapiAsyncObject* async_object);

  static void OrphanImpl(Napi::Env env, std::unique_ptr<ImplBase> impl,
                         uint32_t id);

 private:
  Napi::Value DoCall(const Napi::CallbackInfo& info);
  Napi::Value DoFlush(Napi::Env env, bool reset_argument_cache = true);

  void RequestVSync();

  Napi::Value RunBuffer(uint32_t* buffer, uint32_t length, Napi::Env env);

  template <typename T>
  static T& ReadBuffer(uint32_t* buffer, size_t word_offset) {
    return *reinterpret_cast<T*>(buffer + word_offset);
  }
  template <typename T>
  static T* FromId(uint32_t id);
  NapiAsyncObject* FromAsyncId(uint32_t async_id);
  static void LogTypeError(const char* method, const char* arg_name,
                           uint32_t id);

  Napi::Env env_;
  Napi::ObjectReference js_object_;

  // Stores command and arguments.
  // [0:3] bytes store current buffer length.
  // [4:] bytes store the actual commands.
  uint32_t* buffer_ = nullptr;

  // Stores argument objects.
  Napi::ObjectReference objects_;

  // Stores active context ids.
  Napi::ObjectReference active_contexts_;

  std::shared_ptr<VSyncSource> vsync_source_;
  bool vsync_requested_ = false;

  std::map<uint32_t, std::unique_ptr<ImplBase>> orphans_;

  // Registry for binding objects. JS has ownership.
  // This is static since some objects can be created prior to command buffer.
  static std::map<uint32_t, ScriptWrappable*>& ObjectRegistry();

  // Registry for async objects. They are different from sync objects in:
  // 1. The JS object is created first and asynchronously associated with impl
  // 2. The unique id is given by an "async client", not generated by our side
  std::map<uint32_t, NapiBridge*> async_object_registry_;

  // Defend against recursive flushes.
  bool is_in_flush_ = false;
};

}  // namespace gen_test
}  // namespace lynx

#endif  // THIRD_PARTY_GEN_TEST_NAPI_GEN_TEST_COMMAND_BUFFER_H_
