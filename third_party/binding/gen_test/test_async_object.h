// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
#ifndef THIRD_PARTY_BINDINGS_GEN_TEST_TEST_ASYNC_OBJECT_H_
#define THIRD_PARTY_BINDINGS_GEN_TEST_TEST_ASYNC_OBJECT_H_

#include "third_party/binding/napi/napi_bridge.h"

namespace lynx {
namespace gen_test {

class TestAsyncObject : public binding::ImplBase {
};

}  // namespace gen_test
}  // namespace lynx

#endif  // THIRD_PARTY_BINDINGS_GEN_TEST_TEST_ASYNC_OBJECT_H_
