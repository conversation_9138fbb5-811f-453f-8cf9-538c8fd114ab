#
# This file describes all Blink IDL extended attributes and allowed values.
# If any IDL file uses an extended attribute or values not listed below, the
# build will fail.
# If you would like to add a new extended attribute or value, please:
#     (1) add the extended attribute or value to this file
#     (2) add an explanation to the Blink IDL extended attributes document:
#         IDLExtendedAttributes.md (in this directory)
#     (3) add appropriate test cases to run_bindings_tests.py
#
# The syntax of this file is as follows:
#     - One extended attribute per one line: Name and (optionally) Values.
#     - "Attr" means that the Attr does not take a value, i.e. [Attr].
#     - "Attr=X" means that Attr takes a required value, which must be X;
#       i.e. [Attr=X].
#     - "Attr=X|Y|Z" means that Attr takes a required value, and the valid
#       values are X, Y, and Z, and combinations thereof;
#       e.g. [Attr=X], [Attr=Y], [Attr=X|Z].
#       The separator must be | or &, so [Attr=X&Z] is also valid.
#     - "Attr=|X|Y|Z" means that Attr takes an optional value, whose valid
#       values (if present) are X, Y, and Z, and combinations thereof; e.g.
#       [Attr], [Attr=X], [Attr=Y], [Attr=X|Z], [Attr=X|Y|Z], [Attr=X&Z].
#       Note that including an empty value in the list, as in [Attr=X||Y],
#       is NOT valid: the value is optional, but empty values are not allowed.
#     - "Attr=*" means that Attr takes a required value, which can be
#       arbitrary, and combinations thereof, e.g. [Attr=IndexedDB],
#       [Attr=DeleteFunction], [Attr=X|Y].
#     - "Attr=|*" means that Attr takes an optional value, which can be
#       arbitrary, e.g. [Attr], [Attr=X].
#     - "Attr=X|*" means that Attr takes an required value, which can be
#       arbitrary, but that "X" is standard, e.g. [Attr=X], [Attr=Foo].
#

ActiveScriptWrappable
Affects=Nothing
AllowShared
CEReactions
CSSProperty
CachedAccessor=*
CachedAttribute=*
CallWith=ExecutionContext|Isolate|ScriptState|ThisValue
CheckSecurity=Receiver|ReturnValue
Clamp
Constructor
# FIXME: remove [ConstructorCallWith=Document], as can instead use
# [ConstructorCallWith=ExecutionContext] + toDocument(executionContext)
ConstructorCallWith=ExecutionContext|ScriptState|Document
ContextEnabled=*
CrossOrigin=|Getter|Setter
Custom=|Getter|Setter|LegacyCallAsFunction|PropertyGetter|PropertyEnumerator|PropertyQuery
CustomConstructor
DefaultValue=Undefined
DeprecateAs=*
DoNotCheckConstants
DoNotTestNewObject
EnforceRange
Exposed=*
FeaturePolicy=*
FlexibleArrayBufferView
GetterCallWith=ScriptState
Global=*
HighEntropy=|Direct
HTMLConstructor
ImmutablePrototype
ImplementedAs=*
IsCodeLike
LegacyLenientSetter
LegacyLenientThis
LegacyNoInterfaceObject
LegacyOverrideBuiltIns
LegacyTreatAsPartialInterface
LegacyTreatNonObjectAsNull
LegacyUnenumerableNamedProperties
LegacyUnforgeable
LegacyWindowAlias=*
LegacyWindowAlias_Measure=|*
LegacyWindowAlias_RuntimeEnabled=*
LogActivity=|GetterOnly|SetterOnly
LogAllWorlds
NewObject
NoAllocDirectCall
Measure
MeasureAs=*
NamedConstructor=*
NamedConstructor_CallWith=Document
NamedConstructor_RaisesException
NotEnumerable
PartialInterfaceImplementedAs=*
PermissiveDictionaryConversion
PerWorldBindings
PutForwards=*
RaisesException=|Getter|Setter|Constructor
Reflect=|*
ReflectEmpty=*
ReflectInvalid=*
ReflectMissing=*
ReflectOnly=*
Replaceable
# Valid values for [RuntimeEnabled] are the Runtime Enabled Features, listed in
# third_party/blink/renderer/platform/runtime_enabled_features.json5
RuntimeEnabled=*
# Valid values for [RuntimeCallStatsCounter] are counters defined in
# third_party/blink/renderer/platform/bindings/runtime_call_stats.h
RuntimeCallStatsCounter=*
SameObject
SaveSameObject
SecureContext=|*
Serializable
SetterCallWith=ExecutionContext|Isolate|ScriptState
StringContext=TrustedHTML|TrustedScript|TrustedScriptURL
Transferable
TreatNullAs=EmptyString
URL
Unscopable

DisposeOnDestruction
LogOnDestruction
BufferCommands
EnableTrace
OmitConstants
ImplNamespace=*
MayChain
EnableInterval
Exported
ExportImplGetter
HolderStorageNamespace=*
SharedImpl
AsyncCreated
ForcedSync
FlushedWithinFrame
DataContainer
InOutBuffer
DeferredSetters
SkipForRemote
NeedsNativeSetter
