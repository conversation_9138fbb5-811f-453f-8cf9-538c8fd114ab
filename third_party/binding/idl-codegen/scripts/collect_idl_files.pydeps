# Generated by running:
#   build/print_python_deps.py --root third_party/blink/renderer/bindings/scripts --output third_party/blink/renderer/bindings/scripts/collect_idl_files.pydeps third_party/blink/renderer/bindings/scripts/collect_idl_files.py
../../../../../tools/idl_parser/__init__.py
../../../../../tools/idl_parser/idl_lexer.py
../../../../../tools/idl_parser/idl_node.py
../../../../../tools/idl_parser/idl_parser.py
../../../../ply/__init__.py
../../../../ply/lex.py
../../../../ply/yacc.py
../../../../pyjson5/src/json5/__init__.py
../../../../pyjson5/src/json5/lib.py
../../../../pyjson5/src/json5/parser.py
../../../../pyjson5/src/json5/version.py
../../build/scripts/blinkbuild/__init__.py
../../build/scripts/blinkbuild/name_style_converter.py
blink_idl_lexer.py
blink_idl_parser.py
collect_idl_files.py
utilities.py
web_idl/__init__.py
web_idl/argument.py
web_idl/ast_group.py
web_idl/attribute.py
web_idl/callback_function.py
web_idl/callback_interface.py
web_idl/code_generator_info.py
web_idl/composition_parts.py
web_idl/constant.py
web_idl/constructor.py
web_idl/database.py
web_idl/database_builder.py
web_idl/dictionary.py
web_idl/enumeration.py
web_idl/exposure.py
web_idl/extended_attribute.py
web_idl/file_io.py
web_idl/function_like.py
web_idl/idl_compiler.py
web_idl/idl_type.py
web_idl/includes.py
web_idl/interface.py
web_idl/ir_builder.py
web_idl/ir_map.py
web_idl/literal_constant.py
web_idl/make_copy.py
web_idl/namespace.py
web_idl/operation.py
web_idl/reference.py
web_idl/runtime_enabled_features.py
web_idl/typedef.py
web_idl/union.py
web_idl/user_defined_type.py
web_idl/validator.py
