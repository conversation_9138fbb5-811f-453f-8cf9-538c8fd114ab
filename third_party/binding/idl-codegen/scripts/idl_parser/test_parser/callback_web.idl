/* Copyright (c) 2013 The Chromium Authors. All rights reserved.
   Use of this source code is governed by a BSD-style license that can be
  found in the LICENSE file. */

/* Test Callback productions

Run with --test to generate an AST and verify that all comments accurately
reflect the state of the Nodes.

ERROR Error String
This comment signals that a error of <Error String> is generated.  The error
is not assigned to a node, but are expected in order.

TREE
Type(Name)
  Type(Name)
  Type(Name)
    Type(Name)
    ...
This comment signals that a tree of nodes matching the BUILD comment
symatics should exist.  This is an exact match.
*/


/** TREE
 *Callback(VoidFunc)
 *  Type()
 *    PrimitiveType(void)
 *  Arguments()
 */
callback VoidFunc = void();

/** TREE
 *Callback(VoidFuncLongErr)
 *  Type()
 *    PrimitiveType(void)
 *  Arguments()
 *    Error(Unexpected ).)
 */
callback VoidFuncLongErr = void ( long );

/** TREE
 *Callback(VoidFuncLong)
 *  Type()
 *    PrimitiveType(void)
 *  Arguments()
 *    Argument(L1)
 *      Type()
 *        PrimitiveType(long)
 */
callback VoidFuncLong = void ( long L1 );

/** TREE
 *Callback(VoidFuncLongSequence)
 *  Type()
 *    PrimitiveType(void)
 *  Arguments()
 *    Argument(L1)
 *      Type()
 *        Sequence()
 *          Type()
 *            PrimitiveType(long)
 */
callback VoidFuncLongSequence = void ( sequence<long> L1 );

/** TREE
 *Callback(VoidFuncLongIdent)
 *  Type()
 *    PrimitiveType(void)
 *  Arguments()
 *    Argument(L1)
 *      Type()
 *        Sequence()
 *          Type()
 *            PrimitiveType(long)
 *    Argument(L2)
 *      Type()
 *        Typeref(VoidFuncLongSequence)
 */
callback VoidFuncLongIdent = void ( sequence<long> L1, VoidFuncLongSequence L2 );
