/* Copyright (c) 2013 The Chromium Authors. All rights reserved.
   Use of this source code is governed by a BSD-style license that can be
  found in the LICENSE file. */

/* Test Dictionary productions

Run with --test to generate an AST and verify that all comments accurately
reflect the state of the Nodes.

ERROR Error String
This comment signals that a error of <Error String> is generated.  The error
is not assigned to a node, but are expected in order.

TREE
Type(Name)
  Type(Name)
  Type(Name)
    Type(Name)
    ...
This comment signals that a tree of nodes matching the BUILD comment
symatics should exist.  This is an exact match.
*/


/** TREE
 *Dictionary(MyDict)
 */
dictionary MyDict { };

/** TREE
 *Dictionary(MyDictInherit)
 *  Inherit(Foo)
 */
dictionary MyDictInherit : Foo {};

/** TREE
 *Dictionary(MyDictPartial)
 *  PARTIAL: True
 */
partial dictionary MyDictPartial { };

/** ERROR Unexpected ":" after identifier "MyDictInherit". */
partial dictionary MyDictInherit : Foo {};

/** TREE
 *Dictionary(MyDictBig)
 *  Key(setString)
 *    Type()
 *      StringType(DOMString)
 *    Default() = "Foo"
 *  Key(setLong)
 *    Type()
 *      PrimitiveType(unsigned long long)
 *    Default() = "123"
 *  Key(unsetLong)
 *    Type()
 *      PrimitiveType(long)
 */
dictionary MyDictBig {
  DOMString setString = "Foo";
  unsigned long long setLong = 123;
  long unsetLong;
};

/** TREE
 *Dictionary(MyDictRequired)
 *  Key(setLong)
 *    REQUIRED: True
 *    Type()
 *      PrimitiveType(long)
 */
dictionary MyDictRequired {
  required long setLong;
};

/** ERROR Unexpected "{" after keyword "dictionary". */
dictionary {
  DOMString? setString = null;
};

/** TREE
 *Dictionary(MyDictionaryInvalidOptional)
 *  Key(mandatory)
 *    Type()
 *      StringType(DOMString)
 *  Key(sequenceOfLongWithClamp)
 *    Type()
 *      Sequence()
 *        Type()
 *          PrimitiveType(long)
 *          ExtAttributes()
 *            ExtAttribute(Clamp)
 *  Error(Unexpected keyword "optional" after ">".)
 */
dictionary MyDictionaryInvalidOptional {
  DOMString mandatory;
  sequence<[Clamp] long> sequenceOfLongWithClamp;
  sequence<DOMString> optional;
};

/** ERROR Unexpected identifier "NoColon" after identifier "ForParent". */
dictionary ForParent NoColon {
  DOMString? setString = null;
};

/** TREE
 *Dictionary(MyDictNull)
 *  Key(setString)
 *    Type()
 *      NULLABLE: True
 *      StringType(DOMString)
 *    Default() = "NULL"
 */
dictionary MyDictNull {
  DOMString? setString = null;
};

/** ERROR Unexpected keyword "attribute" after "{". */
dictionary MyDictUnexpectedAttribute {
  attribute DOMString foo = "";
};

/** TREE
 *Dictionary(MyDictRequiredClampNotAppliedToType)
 *  Key(setLong)
 *    REQUIRED: True
 *    Type()
 *      PrimitiveType(long)
 *    ExtAttributes()
 *      ExtAttribute(Clamp)
 */
dictionary MyDictRequiredClampNotAppliedToType {
  [Clamp] required long setLong;
};

/** TREE
 *Dictionary(MyDictRequired)
 *  Key(setLong)
 *    REQUIRED: True
 *    Type()
 *      PrimitiveType(long)
 *    ExtAttributes()
 *      ExtAttribute(XAttr) = "foo"
 */
dictionary MyDictRequired {
  [XAttr = foo] required long setLong;
};

/** TREE
 *Dictionary(MyDictRequired)
 *  Key(setLong)
 *    REQUIRED: True
 *    Type()
 *      PrimitiveType(long)
 *    ExtAttributes()
 *      ExtAttribute(XAttr1)
 *      ExtAttribute(XAttr2)
 */
dictionary MyDictRequired {
  [XAttr1, XAttr2] required long setLong;
};

/** TREE
 *Dictionary(MyDictRequired)
 *  Key(setLong)
 *    REQUIRED: True
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(EnforceRange)
 *    ExtAttributes()
 *      ExtAttribute(Clamp)
 */
dictionary MyDictRequired {
  [Clamp] required [EnforceRange] long setLong;
};

/** TREE
 *Dictionary(MyDictRequired)
 *  Key(setLong)
 *    REQUIRED: True
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(XAttr) = "foo"
 *    ExtAttributes()
 *      ExtAttribute(Clamp)
 */
dictionary MyDictRequired {
  [Clamp] required [XAttr = foo] long setLong;
};

/** TREE
 *Dictionary(MyDictRequired)
 *  Key(setLong)
 *    REQUIRED: True
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(XAttr1)
 *        ExtAttribute(XAttr2)
 *    ExtAttributes()
 *      ExtAttribute(Clamp)
 */
dictionary MyDictRequired {
  [Clamp] required [XAttr1, XAttr2] long setLong;
};

/** TREE
 *Dictionary(MyDict)
 *  Key(setLong)
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(Clamp)
 */
dictionary MyDict {
  [Clamp] long setLong;
};

/** TREE
 *Dictionary(MyDict)
 *  Key(setLong)
 *    Type()
 *      PrimitiveType(long)
 *    ExtAttributes()
 *      ExtAttribute(XAttr)
 */
dictionary MyDict {
  [XAttr] long setLong;
};

/** TREE
 *Dictionary(MyDict)
 *  Key(setLong)
 *    Type()
 *      PrimitiveType(long)
 *    ExtAttributes()
 *      ExtAttribute(XAttr) = "foo"
 */
dictionary MyDict {
  [XAttr = foo] long setLong;
};

/** TREE
 *Dictionary(MyDict)
 *  Key(setLong)
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(Clamp)
 *    ExtAttributes()
 *      ExtAttribute(XAttr)
 */
dictionary MyDict {
  [XAttr, Clamp] long setLong;
};

/** TREE
 *Dictionary(MyDict)
 *  Key(foo)
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(Clamp)
 *    Default() = "2"
 */
dictionary MyDict {
  [Clamp] long foo = 2;
};

/** TREE
 *Dictionary(MyDict)
 *  Key(setString)
 *    Type()
 *      StringType(DOMString)
 *    ExtAttributes()
 *      ExtAttribute(XAttr)
 */
dictionary MyDict {
  [XAttr] DOMString setString;
};

/** TREE
 *Dictionary(MyDict)
 *  Key(setLong)
 *    Type()
 *      PrimitiveType(long)
 *    ExtAttributes()
 *      ExtAttribute(XAttr) = "bar"
 */
dictionary MyDict {
  [XAttr = bar] long setLong;
};

/** TREE
 *Dictionary(MyDict)
 *  Key(setLong)
 *    Type()
 *      PrimitiveType(long)
 *    ExtAttributes()
 *      ExtAttribute(XAttr1)
 *      ExtAttribute(XAttr2)
 */
dictionary MyDict {
  [XAttr1, XAttr2] long setLong;
};
