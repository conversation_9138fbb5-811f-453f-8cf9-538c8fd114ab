/* Copyright (c) 2013 The Chromium Authors. All rights reserved.
   Use of this source code is governed by a BSD-style license that can be
  found in the LICENSE file. */

/* Test Interface productions

Run with --test to generate an AST and verify that all comments accurately
reflect the state of the Nodes.

ERROR Error String
This comment signals that a error of <Error String> is generated.  The error
is not assigned to a node, but are expected in order.

TREE
Type(Name)
  Type(Name)
  Type(Name)
    Type(Name)
    ...
This comment signals that a tree of nodes matching the BUILD comment
symatics should exist.  This is an exact match.
*/


/** TREE
 *Interface(MyIFace)
 */
interface MyIFace { };

/** TREE
 *Interface(MyIFaceInherit)
 *  Inherit(Foo)
 */
interface MyIFaceInherit : Foo {};

/** TREE
 *Interface(MyIFacePartial)
 *  PARTIAL: True
 */
partial interface MyIFacePartial { };

/** ERROR Unexpected ":" after identifier "MyIFaceInherit". */
partial interface MyIFaceInherit : Foo {};

/** TREE
 *Interface(IFaceMixin)
 *  MIXIN: True
 */
interface mixin IFaceMixin {};

/** ERROR Unexpected ":" after identifier "IFaceMixinInherit". */
interface mixin IFaceMixinInherit : Foo {};

/** TREE
 *Interface(PartialIFaceMixin)
 *  PARTIAL: True
 *  MIXIN: True
 */
partial interface mixin PartialIFaceMixin {};

/** TREE
 *Interface(IFaceMixin)
 *  MIXIN: True
 *  Error(Unexpected keyword "static" after "{".)
 */
interface mixin IFaceMixin {
  static attribute integer staticAttribute;
};

/** TREE
 *Interface(IFaceMixin)
 *  MIXIN: True
 *  Const(pi)
 *    PrimitiveType(double)
 *    Value() = "3.14159"
 *  Operation(foo)
 *    Arguments()
 *    Type()
 *      PrimitiveType(void)
 *  Attribute(baz)
 *    Type()
 *      PrimitiveType(long)
 *    ExtAttributes()
 *      ExtAttribute(XAttr)
 *  Attribute(bar)
 *    Type()
 *      StringType(DOMString)
 *  Attribute(readonlyAttr)
 *    READONLY: True
 *    Type()
 *      StringType(DOMString)
 */
interface mixin IFaceMixin {
  const double pi = 3.14159;
  void foo();
  [XAttr] attribute long baz;
  attribute DOMString bar;
  readonly attribute DOMString readonlyAttr;
};

/** TREE
 *Interface(MyIFaceMissingArgument)
 *  Operation(foo)
 *    Arguments()
 *      Argument(arg)
 *        Type()
 *          StringType(DOMString)
 *      Error(Missing argument.)
 *    Type()
 *      PrimitiveType(void)
 */
interface MyIFaceMissingArgument {
  void foo(DOMString arg, );
};

/** TREE
 *Error(Unexpected keyword "double" after keyword "readonly".)
 */
interface MyIFaceMissingAttribute {
  readonly double foo;
};

/** TREE
 *Interface(MyIFaceContainsUnresolvedConflictDiff)
 *  Operation(foo)
 *    Arguments()
 *    Type()
 *      StringType(DOMString)
 *  Error(Unexpected "<" after ";".)
 */
interface MyIFaceContainsUnresolvedConflictDiff {
    DOMString foo();
<<<<<< ours
    DOMString bar();
    iterable<long>;
======
>>>>>> theirs
};

/** TREE
 *Interface(MyIFaceWrongRecordKeyType)
 *  Operation(foo)
 *    Arguments()
 *      Argument(arg)
 *        Type()
 *          Error(Unexpected identifier "int" after "<".)
 *    Type()
 *      PrimitiveType(void)
 */
interface MyIFaceWrongRecordKeyType {
  void foo(record<int, ByteString> arg);
};

/** TREE
 *Interface(IFaceAllowedKeywords)
 *  Attribute(async)
 *    Type()
 *      PrimitiveType(long)
 *  Operation(includes)
 *    Arguments()
 *      Argument(async)
 *        Type()
 *          PrimitiveType(long)
 *      Argument(constructor)
 *        Type()
 *          PrimitiveType(long)
 *    Type()
 *      PrimitiveType(void)
 *  Attribute(constructor)
 *    Type()
 *      PrimitiveType(long)
 *  Error(Unexpected constructor.)
 */
interface IFaceAllowedKeywords {
  attribute long async;
  void includes(long async, long constructor);
  attribute long _constructor;
  attribute long constructor;
};

/** TREE
 *Interface(MyIfaceDefalutValue)
 *  Operation(foo)
 *    Arguments()
 *      Argument(arg)
 *        OPTIONAL: True
 *        Type()
 *          Sequence()
 *            Type()
 *              StringType(DOMString)
 *        Default() = "[]"
 *    Type()
 *      PrimitiveType(void)
 *  Operation(bar)
 *    Arguments()
 *      Argument(arg)
 *        OPTIONAL: True
 *        Type()
 *          Typeref(MyDict)
 *        Default() = "{}"
 *    Type()
 *      PrimitiveType(void)
 *  Operation(baz)
 *    Arguments()
 *      Argument(arg)
 *        OPTIONAL: True
 *        Type()
 *          NULLABLE: True
 *          StringType(DOMString)
 *        Default() = "NULL"
 *    Type()
 *      PrimitiveType(void)
 */
interface MyIfaceDefalutValue {
  void foo(optional sequence<DOMString> arg = []);
  void bar(optional MyDict arg = {});
  void baz(optional DOMString? arg = null);
};

/** TREE
 *Interface(MyIfaceWithRecords)
 *  Operation(foo)
 *    Arguments()
 *      Argument(arg)
 *        Type()
 *          Record()
 *            StringType(DOMString)
 *            Type()
 *              PrimitiveType(long)
 *              ExtAttributes()
 *                ExtAttribute(EnforceRange)
 *    Type()
 *      PrimitiveType(void)
 *  Operation(bar)
 *    Arguments()
 *      Argument(arg1)
 *        Type()
 *          Typeref(int)
 *      Argument(arg2)
 *        Type()
 *          Record()
 *            StringType(ByteString)
 *            Type()
 *              PrimitiveType(float)
 *    Type()
 *      PrimitiveType(double)
 */
interface MyIfaceWithRecords {
  void foo(record<DOMString, [EnforceRange] long> arg);
  double bar(int arg1, record<ByteString, float> arg2);
};

/** TREE
 *Interface(MyIFaceBig)
 *  Const(longValue)
 *    PrimitiveType(long)
 *    Value() = "123"
 *  Const(longValue2)
 *    PrimitiveType(long long)
 *    Value() = "123"
 *  Attribute(myString)
 *    Type()
 *      StringType(DOMString)
 *  Attribute(readOnlyString)
 *    READONLY: True
 *    Type()
 *      StringType(DOMString)
 *  Attribute(staticString)
 *    STATIC: True
 *    Type()
 *      StringType(DOMString)
 *  Operation(myFunction)
 *    Arguments()
 *      Argument(myLong)
 *        Type()
 *          PrimitiveType(long long)
 *    Type()
 *      PrimitiveType(void)
 *  Operation(staticFunction)
 *    STATIC: True
 *    Arguments()
 *      Argument(myLong)
 *        Type()
 *          PrimitiveType(long long)
 *    Type()
 *      PrimitiveType(void)
 */
interface MyIFaceBig {
  const long longValue = 123;
  const long long longValue2 = 123;
  attribute DOMString myString;
  readonly attribute DOMString readOnlyString;
  static attribute DOMString staticString;
  void myFunction(long long myLong);
  static void staticFunction(long long myLong);
};

/** TREE
 *Interface(MyIFaceSpecials)
 *  Operation(set)
 *    SETTER: True
 *    Arguments()
 *      Argument(property)
 *        Type()
 *          StringType(DOMString)
 *    Type()
 *      PrimitiveType(void)
 *  Operation()
 *    GETTER: True
 *    Arguments()
 *      Argument(property)
 *        Type()
 *          StringType(DOMString)
 *    Type()
 *      PrimitiveType(double)
 */
interface MyIFaceSpecials {
  setter void set(DOMString property);
  getter double (DOMString property);
};

/** TREE
 *Interface(MyIFaceConstructor)
 *  Constructor()
 *    Arguments()
 *  Constructor()
 *    Arguments()
 *      Argument(str)
 *        Type()
 *          StringType(DOMString)
 *      Argument(param)
 *        OPTIONAL: True
 *        Type()
 *          PrimitiveType(long)
 *  Constructor()
 *    Arguments()
 *      Argument(node)
 *        Type()
 *          Typeref(Node)
 *    ExtAttributes()
 *      ExtAttribute(Custom)
 */
interface MyIFaceConstructor {
  constructor();
  constructor(DOMString str, optional long param);
  [Custom] constructor(Node node);
};

/** TREE
 *Interface(PartialIFaceConstructor)
 *  PARTIAL: True
 *  Error(Unexpected keyword "constructor" after "{".)
 */
partial interface PartialIFaceConstructor {
  constructor();
};

/** TREE
 *Interface(MyIFaceStringifiers)
 *  Stringifier()
 *  Stringifier()
 *    Operation()
 *      Arguments()
 *      Type()
 *        StringType(DOMString)
 *  Stringifier()
 *    Operation(namedStringifier)
 *      Arguments()
 *      Type()
 *        StringType(DOMString)
 *  Stringifier()
 *    Attribute(stringValue)
 *      Type()
 *        StringType(DOMString)
 */
interface MyIFaceStringifiers {
  stringifier;
  stringifier DOMString ();
  stringifier DOMString namedStringifier();
  stringifier attribute DOMString stringValue;
};

/** TREE
 *Interface(MyExtendedAttributeInterface)
 *  Operation(method)
 *    Arguments()
 *    Type()
 *      PrimitiveType(void)
 *    ExtAttributes()
 *      ExtAttribute(Attr)
 *      ExtAttribute(MethodIdentList) = "['Foo', 'Bar']"
 *  ExtAttributes()
 *    ExtAttribute(MyExtendedAttribute)
 *    ExtAttribute(MyExtendedIdentListAttribute) = "['Foo', 'Bar', 'Baz']"
 */
[MyExtendedAttribute,
 MyExtendedIdentListAttribute=(Foo, Bar, Baz)]
interface MyExtendedAttributeInterface {
  [Attr, MethodIdentList=(Foo, Bar)] void method();
};

/** TREE
 *Interface(MyIfacePromise)
 *  Operation(method1)
 *    Arguments()
 *    Type()
 *      Promise(Promise)
 *        Type()
 *          PrimitiveType(void)
 *  Operation(method2)
 *    Arguments()
 *    Type()
 *      Promise(Promise)
 *        Type()
 *          PrimitiveType(long)
 *  Operation(method3)
 *    Arguments()
 *    Type()
 *      Promise(Promise)
 *        Type()
 *          Any()
 *  Error(Unexpected identifier "method4" after keyword "Promise".)
 */
interface MyIfacePromise {
  Promise<void> method1();
  Promise<long> method2();
  Promise<any> method3();
  Promise method4();
};

/** TREE
 *Interface(MyIfaceIterable)
 *  Iterable()
 *    Type()
 *      PrimitiveType(long)
 *  Iterable()
 *    Type()
 *      PrimitiveType(double)
 *    Type()
 *      StringType(DOMString)
 *  Iterable()
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(Clamp)
 *  Iterable()
 *    Type()
 *      StringType(DOMString)
 *      ExtAttributes()
 *        ExtAttribute(TreatNullAs) = "EmptyString"
 *  Iterable()
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(Clamp)
 *        ExtAttribute(XAttr)
 *  Iterable()
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(Clamp)
 *    Type()
 *      PrimitiveType(long long)
 *      ExtAttributes()
 *        ExtAttribute(EnforceRange)
 *  AsyncIterable()
 *    Type()
 *      PrimitiveType(double)
 *    Type()
 *      StringType(DOMString)
 */
interface MyIfaceIterable {
  iterable<long>;
  iterable<double, DOMString>;
  iterable<[Clamp] long>;
  iterable<[TreatNullAs=EmptyString] DOMString>;
  iterable<[Clamp, XAttr] long>;
  iterable<[Clamp] long, [EnforceRange] long long>;
  async iterable<double, DOMString>;
};

/** TREE
 *Interface(MyIfaceMaplike)
 *  Maplike()
 *    READONLY: True
 *    Type()
 *      PrimitiveType(long)
 *    Type()
 *      StringType(DOMString)
 *  Maplike()
 *    Type()
 *      PrimitiveType(double)
 *    Type()
 *      PrimitiveType(boolean)
 *  Maplike()
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(Clamp)
 *    Type()
 *      StringType(DOMString)
 *      ExtAttributes()
 *        ExtAttribute(XAttr)
 */
interface MyIfaceMaplike {
  readonly maplike<long, DOMString>;
  maplike<double, boolean>;
  maplike<[Clamp] long, [XAttr] DOMString>;
};

/** TREE
 *Interface(MyIfaceSetlike)
 *  Setlike()
 *    READONLY: True
 *    Type()
 *      PrimitiveType(long)
 *  Setlike()
 *    Type()
 *      PrimitiveType(double)
 *  Setlike()
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(EnforceRange)
 */
interface MyIfaceSetlike {
  readonly setlike<long>;
  setlike<double>;
  setlike<[EnforceRange] long>;
};

/** TREE
 *Interface(MyIfaceFrozenArray)
 *  Attribute(foo)
 *    READONLY: True
 *    Type()
 *      FrozenArray()
 *        Type()
 *          StringType(DOMString)
 *  Attribute(bar)
 *    READONLY: True
 *    Type()
 *      FrozenArray()
 *        Type()
 *          StringType(DOMString)
 *          ExtAttributes()
 *            ExtAttribute(TreatNullAs) = "EmptyString"
 */
interface MyIfaceFrozenArray {
  readonly attribute FrozenArray<DOMString> foo;
  readonly attribute FrozenArray<[TreatNullAs=EmptyString] DOMString> bar;
};

/** TREE
 *Interface(MyIfaceUnion)
 *  Attribute(foo)
 *    Type()
 *      UnionType()
 *        Type()
 *          StringType(DOMString)
 *        Type()
 *          PrimitiveType(long)
 */
interface MyIfaceUnion {
  attribute (DOMString or long) foo;
};

/** TREE
 *Interface(MyIfaceAttributeRestClamp)
 *  Attribute(myLong)
 *    Type()
 *      PrimitiveType(long)
 *      ExtAttributes()
 *        ExtAttribute(Clamp)
 */
interface MyIfaceAttributeRestClamp {
  attribute [Clamp] long myLong;
};

/** TREE
 *Interface(MyIFaceArgumentWithAnnotatedType1)
 *  Operation(myFunction)
 *    Arguments()
 *      Argument(myLong)
 *        OPTIONAL: True
 *        Type()
 *          PrimitiveType(long long)
 *          ExtAttributes()
 *            ExtAttribute(Clamp)
 *    Type()
 *      PrimitiveType(void)
 */
interface MyIFaceArgumentWithAnnotatedType1 {
  void myFunction(optional [Clamp] long long myLong);
};

/** TREE
 *Interface(MyIFaceArgumentWithAnnotatedType2)
 *  Operation(voidMethodTestArgumentWithExtAttribute1)
 *    Arguments()
 *      Argument(myLong)
 *        Type()
 *          PrimitiveType(long long)
 *          ExtAttributes()
 *            ExtAttribute(Clamp)
 *        ExtAttributes()
 *          ExtAttribute(XAttr)
 *    Type()
 *      PrimitiveType(void)
 *  Operation(voidMethodTestArgumentWithExtAttribute2)
 *    Arguments()
 *      Argument(longArg)
 *        Type()
 *          PrimitiveType(long)
 *          ExtAttributes()
 *            ExtAttribute(EnforceRange)
 *    Type()
 *      PrimitiveType(void)
 */
interface MyIFaceArgumentWithAnnotatedType2 {
  void voidMethodTestArgumentWithExtAttribute1([Clamp, XAttr] long long myLong);
  void voidMethodTestArgumentWithExtAttribute2([EnforceRange] long longArg);
};

/** TREE
 *Interface(InterfaceConstructors)
 *  ExtAttributes()
 *    ExtAttribute(Constructor)
 *    ExtAttribute(Constructor)
 *      Arguments()
 *        Argument(doubleArg)
 *          Type()
 *            PrimitiveType(double)
 *    ExtAttribute(CustomConstructor)
 *    ExtAttribute(CustomConstructor)
 *      Arguments()
 *        Argument(doubleArg)
 *          Type()
 *            PrimitiveType(double)
 *    ExtAttribute(NamedConstructor) = "Audio"
 *    ExtAttribute(NamedConstructor)
 *      Call(Audio)
 *        Arguments()
 *          Argument(src)
 *            Type()
 *              StringType(DOMString)
 */
[
    Constructor,
    Constructor(double doubleArg),
    CustomConstructor,
    CustomConstructor(double doubleArg),
    NamedConstructor=Audio,
    NamedConstructor=Audio(DOMString src)
] interface InterfaceConstructors { };

/** TREE
 *Interface(InterfaceExposed)
 *  ExtAttributes()
 *    ExtAttribute(Exposed) = "Window"
 *    ExtAttribute(Exposed) = "['Window', 'Worker']"
 *    ExtAttribute(Exposed)
 *      Arguments()
 *        Argument(Feature1)
 *          Type()
 *            Typeref(Window)
 *    ExtAttribute(Exposed)
 *      Arguments()
 *        Argument(Feature1)
 *          Type()
 *            Typeref(Window)
 *        Argument(Feature2)
 *          Type()
 *            Typeref(Worker)
 */
[
    Exposed=Window,
    Exposed=(Window, Worker),
    Exposed(Window Feature1),
    Exposed(Window Feature1, Worker Feature2)
] interface InterfaceExposed { };

/** TREE
 *Interface(InterfaceExposedError)
 *  ExtAttributes()
 *    ExtAttribute(Exposed)
 *      Arguments()
 *        Error(Unexpected ,.)
 */
[ Exposed(Window, Worker) ] interface InterfaceExposedError { };

/** TREE
 *Includes(Foo)
 *  REFERENCE: Bar
 */
Foo includes Bar;
