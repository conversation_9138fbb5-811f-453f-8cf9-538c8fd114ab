/* Copyright (c) 2013 The Chromium Authors. All rights reserved.
   Use of this source code is governed by a BSD-style license that can be
  found in the LICENSE file. */

/* Test Typedef productions

Run with --test to generate an AST and verify that all comments accurately
reflect the state of the Nodes.

ERROR Error String
This comment signals that a error of <Error String> is generated.  The error
is not assigned to a node, but are expected in order.

TREE
Type(Name)
  Type(Name)
  Type(Name)
    Type(Name)
    ...
This comment signals that a tree of nodes matching the BUILD comment
symatics should exist.  This is an exact match.
*/


/** TREE
 *Typedef(MyLong)
 *  Type()
 *    PrimitiveType(long)
 */
typedef long MyLong;

/** TREE
 *Typedef(MyLong)
 *  Type()
 *    PrimitiveType(long)
 *    ExtAttributes()
 *      ExtAttribute(foo)
 */
typedef [foo] long MyLong;

/** TREE
 *Typedef(MyLongSequence)
 *  Type()
 *    Sequence()
 *      Type()
 *        PrimitiveType(long)
 */
typedef sequence<long> MyLongSequence;

/** TREE
 *Typedef(MyTypeSequence)
 *  Type()
 *    Sequence()
 *      Type()
 *        Typeref(MyType)
 */
typedef sequence<MyType> MyTypeSequence;

/** TREE
 *Typedef(MyLongLong)
 *  Type()
 *    PrimitiveType(long long)
 */
typedef long long MyLongLong;

/** TREE
 *Typedef(MyULong)
 *  Type()
 *    PrimitiveType(unsigned long)
 */
typedef unsigned long MyULong;

/** TREE
 *Typedef(MyULongLong)
 *  Type()
 *    PrimitiveType(unsigned long long)
 */
typedef unsigned long long MyULongLong;

/** TREE
 *Typedef(MyString)
 *  Type()
 *    StringType(DOMString)
 */
typedef DOMString MyString;

/** TREE
 *Typedef(MyObject)
 *  Type()
 *    PrimitiveType(object)
 */
typedef object MyObject;

/** TREE
 *Typedef(MyFloat)
 *  Type()
 *    PrimitiveType(float)
 */
typedef float MyFloat;

/** TREE
 *Typedef(MyUFloat)
 *  Type()
 *    PrimitiveType(float)
 *      UNRESTRICTED: True
 */
typedef unrestricted float MyUFloat;

/** TREE
 *Typedef(MyDouble)
 *  Type()
 *    PrimitiveType(double)
 */
typedef double MyDouble;

/** TREE
 *Typedef(MyUDouble)
 *  Type()
 *    PrimitiveType(double)
 *      UNRESTRICTED: True
 */
typedef unrestricted double MyUDouble;

/** TREE
 *Typedef(MyBool)
 *  Type()
 *    PrimitiveType(boolean)
 */
typedef boolean MyBool;

/** TREE
 *Typedef(MyByte)
 *  Type()
 *    PrimitiveType(byte)
 */
typedef byte MyByte;

/** TREE
 *Typedef(MyOctet)
 *  Type()
 *    PrimitiveType(octet)
 */
typedef octet MyOctet;

/** TREE
 *Typedef(MyRecord)
 *  Type()
 *    Record()
 *      StringType(ByteString)
 *      Type()
 *        Typeref(int)
 */
typedef record<ByteString, int> MyRecord;

/** TREE
 *Typedef(MyInvalidRecord)
 *  Type()
 *    Error(Unexpected keyword "double" after "<".)
 */
typedef record<double, ByteString> MyInvalidRecord;

/** TREE
 *Typedef(MyAnnotationInUnion)
 *  Type()
 *    UnionType()
 *      Type()
 *        StringType(DOMString)
 *        ExtAttributes()
 *          ExtAttribute(TreatNullAs) = "EmptyString"
 *      Type()
 *        PrimitiveType(long)
 *        ExtAttributes()
 *          ExtAttribute(Clamp)
 */
typedef ([TreatNullAs=EmptyString] DOMString or [Clamp] long) MyAnnotationInUnion;

/** TREE
 *Typedef(Date)
 *  Type()
 *    PrimitiveType(double)
 */
typedef double Date;

/** TREE
 *Typedef(StringSequenceOrNull)
 *  Type()
 *    NULLABLE: True
 *    Sequence()
 *      Type()
 *        StringType(DOMString)
 */
typedef sequence<DOMString>? StringSequenceOrNull;
