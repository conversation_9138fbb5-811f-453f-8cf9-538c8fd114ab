{% from 'opts_hardcode.js.tmpl' import overload_resolvers, partial_data_opt %}
{% include 'lynx_copyright_block_js.txt' %}

'use strict';

let {{interface_name}}CB;

function str2view(str, view, start) {
  for (let i = 0, strLen = str.length; i < strLen; ++i) {
    // Always little-endian to adapt to native conversion util.
    view.setInt16(start * 4 + i * 2, str.charCodeAt(i), true);
  }
}

function getViewType(view) {
  const viewType = view.constructor.name;
  switch (view.BYTES_PER_ELEMENT) {
    case 1:
      switch (viewType) {
        case 'Int8Array':
          return 1;
        case 'Uint8Array':
          return 2;
        default:
          // Uint8ClampedArray
          return 3;
      }
      break;
    case 2:
      switch (viewType) {
        case 'Int16Array':
          return 4;
        default:
          // Uint16Array
          return 5;
      }
      break;
    case 4:
      switch (viewType) {
        case 'Int32Array':
          return 6;
        case 'Uint32Array':
          return 7;
        default:
          // Float32Array
          return 8;
      }
      break;
    case 8:
      switch (viewType) {
        case 'Float64Array':
          return 9;
        case 'BigInt64Array':
          return 10;
        default:
          // BigUint64Array
          return 11;
      }
      break;
    default:
      // DataView
      return 12;
  }
}

const commandBufferCreator = function(appendTarget, idGen) {
  if ({{interface_name}}CB) return {{interface_name}}CB;
  const cb = appendTarget.getCommandBuffer();
  const buffer = cb.buffer;

  const uint8View = new Uint8Array(buffer);
  const float32View = new Float32Array(buffer);
  const int32View = new Int32Array(buffer);
  const uint32View = new Uint32Array(buffer);
  const dataView = new DataView(buffer);

  const objects = cb.objects;
  const active = cb.active;

  // Flush at 4 * 40 = 160KB, which is 80% of total buffer size.
  const flushThres = 40 * 1024;
  // Current cutoff for long commands is 32KB, which is well below the 40KB free space.
  const cmdLengthCutoff = 8192;
  const isLittleEndian = new Uint16Array(new Uint8Array([1, 0]).buffer)[0] === 1;

  {% for cls in async_classes %}
  let {{cls}} = appendTarget.{{cls}}
  {% endfor %}

  {% for method in methods %}
  function {{method.name}}{{method.overload_index or ('base' if overloads_child_only[method.name] == true)}}({{ method.arguments | map(attribute='name') | join(', ')}}) {
    {% if interface_name.startswith(partial_data_opt_prefix) %}
    {{ partial_data_opt(interface_name, method) }}
    {% endif %}
    {% set ns = namespace(count = 2) %}
{#                                                                            #}
    {% for argument in method.arguments %}
    {% if argument.async_created %}
    if ({{argument.name}} && !({{argument.name}} instanceof {{argument.idl_type}})) {
      throw new TypeError('Invalid type for arg {{argument.index}}, {{argument.idl_type}} expected');
    }
    {% endif %}
    {% endfor %}
{#                                                                            #}
    {% if method.return_async %}
    let id = idGen.generate();
    let puppet = new {{method.idl_type}}(id);
    puppet.__id = id;
    objects.push(puppet);
    {% endif %}
{#                                                                            #}
    let end = uint32View[0];
    uint32View[end] = {{method.buffered_method_id}};
    {% if method.flushed_within_frame %}
    const contextId = this.__id;
    uint32View[end + 1] = contextId;
    {% else %}
    uint32View[end + 1] = this.__id;
    {% endif %}

    {% for argument in method.arguments %}
    {% if argument.is_boolean_type %}
    uint32View[end + {{ns.count}}] = {{argument.name}};
    {% set ns.count = ns.count + 1 %}
    {% elif argument.is_numeric_type %}
    {% if argument.idl_type == 'unrestricted float' %}
    float32View[end + {{ns.count}}] = {{argument.name}};
    {% elif argument.idl_type == 'long' or argument.idl_type == 'short' or argument.idl_type == 'byte' %}
    int32View[end + {{ns.count}}] = {{argument.name}};
    {% elif argument.idl_type == 'long long' or argument.idl_type == 'unsigned long long' or argument.idl_type == 'unrestricted double' %}
    dataView.setFloat64((end + {{ns.count}}) * 4, {{argument.name}}, isLittleEndian);
    {% set ns.count = ns.count + 1 %}
    {% else %}
    uint32View[end + {{ns.count}}] = {{argument.name}};
    {% endif %}
    {% set ns.count = ns.count + 1 %}
    {% elif argument.is_string_type %}
    let {{argument.name}}Len = {{argument.name}}.length; // throws when null
    if ({{argument.name}}Len / 2 > cmdLengthCutoff) {
      this.{{method.name}}_(...arguments);
      return;
    }
    uint32View[end + {{ns.count}}] = {{argument.name}}Len;
    {% set ns.count = ns.count + 1 %}
{#                                                                            #}
    {% elif argument.is_wrapper_type %}
    uint32View[end + {{ns.count}}] = {{argument.name}}{{'?' if argument.is_nullable}}.__id;
    {% set ns.count = ns.count + 1 %}
    {% if not method.is_sync %}
    objects.push({{argument.name}});
    {% endif %}
{#                                                                            #}
    {% elif argument.is_sequence_type %}
    {# TODO(yuyifei): Currently assumes sequence<string> or sequence<uint32> #}
    uint32View[end + {{ns.count}}] = {{argument.name}}.length;
    {% set ns.count = ns.count + 1 %}
{#                                                                            #}
    {% elif argument.is_typed_array %}{# This needs to handle arrays as well #}
    {% if argument.is_nullable %}
    let {{argument.name}}Len = {{argument.name}}?.byteLength / 4;
    {% else %}
    let {{argument.name}}Len = {{argument.name}}.byteLength / 4; // throws when null
    {% endif %}
    if (!{{argument.name}}Len) {
      // Convert array to typed array.
      {{argument.name}} = arguments[{{argument.index}}] = new {{argument.idl_type}}({{argument.name}});
    }
    {{argument.name}}Len = {{argument.name}}.length;
    if ({{argument.name}}Len > cmdLengthCutoff) {
      // Call binding when the data is too large.
      this.{{method.name}}_(...arguments);
      return;
    }
    uint32View[end + {{ns.count}}] = {{argument.name}}Len;
    {% set ns.count = ns.count + 1 %}
{#                                                                            #}
    {% elif argument.idl_type == 'ArrayBuffer' or argument.idl_type == 'ArrayBufferView' %}
    {% if argument.is_nullable %}
    let {{argument.name}}Len = {{argument.name}}?.byteLength / 4;
    {% else %}
    let {{argument.name}}Len = {{argument.name}}.byteLength / 4; // throws when null
    {% endif %}{# nullable #}
    if ({{argument.name}}Len > cmdLengthCutoff) {
      // Call binding when the data is too large.
      this.{{method.name}}_(...arguments);
      return;
    }
    {% if argument.idl_type == 'ArrayBuffer' %}
    const {{argument.name}}BufferView = new Uint8Array({{argument.name}});
    {% if argument.is_nullable %}
    uint32View[end + {{ns.count}}] = ({{argument.name}} == null);
    {% set ns.count = ns.count + 1 %}
    {% endif %}
    uint32View[end + {{ns.count}}] = {{argument.name}}BufferView.byteLength;
    {% set ns.count = ns.count + 1 %}
    {% else %}{# ArrayBufferView #}
    let {{argument.name}}BufferView;
    if (ArrayBuffer.isView({{argument.name}})) {
      {{argument.name}}BufferView = new Uint8Array({{argument.name}}.buffer, {{argument.name}}.byteOffset, {{argument.name}}.byteLength);
      uint32View[end + {{ns.count}}] = getViewType({{argument.name}});
    } else {
      {{argument.name}}BufferView = new Uint8Array({{argument.name}});
      uint32View[end + {{ns.count}}] = 0;
    }
    {% set ns.count = ns.count + 1 %}
    uint32View[end + {{ns.count}}] = {{argument.name}}BufferView.byteLength;
    {% set ns.count = ns.count + 1 %}
    {% endif %}
    {% endif %}
{#                                                                            #}
    {% endfor %}
    {% if method.return_async %}
    uint32View[end + {{ns.count}}] = id;
    {% set ns.count = ns.count + 1 %}
    {% endif %}
    end += {{ns.count}};

    {% for argument in method.arguments if argument.is_typed_array or argument.is_sequence_type %}
    {% if argument.idl_type == 'Float32Array' %}
    float32View.set({{argument.name}}, end);
    end += {{argument.name}}.length;
    {% elif argument.idl_type == 'Int32Array' %}
    int32View.set({{argument.name}}, end);
    end += {{argument.name}}.length;
    {% elif argument.idl_type == 'Uint32Array' %}
    uint32View.set({{argument.name}}, end);
    end += {{argument.name}}.length;
    {% elif argument.is_sequence_type %}
    {# TODO(yuyifei): Currently assumes sequence<string> or sequence<uint32> #}
    {% if argument.sequence_element_type == 'String' %}
    for (let s of {{argument.name}}) {
      uint32View[end] = s.length;
      str2view(s, dataView, end + 1);
      end += Math.ceil(s.length / 2) + 1;
    }
    {% else %}
    uint32View.set({{argument.name}}, end);
    end += {{argument.name}}.length;
    {% endif %}{# sequence_element_type #}
    {% endif %}
    {% endfor %}
{#                                                                            #}
    {% for argument in method.arguments if argument.is_string_type %}
    str2view({{argument.name}}, dataView, end);
    end += Math.ceil({{argument.name}}Len / 2);
    {% endfor %}
{#                                                                            #}
    {% for argument in method.arguments if argument.idl_type == 'ArrayBuffer' or argument.idl_type == 'ArrayBufferView' %}
    uint8View.set({{argument.name}}BufferView, end * 4);
    end += Math.ceil({{argument.name}}BufferView.byteLength / 4);
    {% endfor %}

    uint32View[0] = end;

    {% if method.is_sync %}
    const result = cb.call();
    objects.length = 0;
    return result;
    {% else %}
    if (end > flushThres{{ ' || !active[contextId]' if method.flushed_within_frame }}) {
      {% if method.flushed_within_frame %}
      active[contextId] = true;
      {% endif %}
      cb.flush();
    }
    {% if method.return_async %}
    return puppet;
    {% endif %}
    {% endif %}{# is_sync #}
  }

  {% endfor %}{# for method in methods #}
{#                                                                            #}
  {% if interface_name.startswith(overload_resolvers_prefix) %}
  {{ overload_resolvers(interface_name, methods, overloads_child_only) }}
  {% endif %}
{#                                                                            #}
  {{interface_name}}CB = {
    {% for method in methods %}
    {% if not method.overload_index or method.overloads %}
    {{method.name}}: {{method.name}},
    {% endif %}
    {% endfor %}
  };

  return {{interface_name}}CB;
};

let protoHooked = false;

function hook{{interface_name}}(appendTarget, context, idGen) {
  const commandBuffer = commandBufferCreator(appendTarget, idGen);
  if (protoHooked) return;
  protoHooked = true;
  let ctxProto = context.__proto__;
  {% for method in methods %}
  {% if not method.overload_index or method.overloads %}
  {% if method.slow_if_buffered %}
  ctxProto.{{method.name}} = ctxProto.{{method.name}}_;
  {# exposed for tests #}
  ctxProto.{{method.name}}$ = commandBuffer.{{method.name}};
  {% else %}
  ctxProto.{{method.name}} = commandBuffer.{{method.name}};
  {% endif %}
  {% endif %}
  {% endfor %}

  {% for constant in constants %}
  ctxProto.{{constant.name}} = {{constant.value}};
  {% endfor %}
}

export { hook{{interface_name}} };