=========================
Mako Templates for Python
=========================

Mako is a template library written in Python. It provides a familiar, non-XML 
syntax which compiles into Python modules for maximum performance. <PERSON><PERSON>'s 
syntax and API borrows from the best ideas of many others, including Django
templates, Ch<PERSON>tah, Myghty, and Genshi. Conceptually, Mako is an embedded 
Python (i.e. Python Server Page) language, which refines the familiar ideas
of componentized layout and inheritance to produce one of the most 
straightforward and flexible models available, while also maintaining close 
ties to Python calling and scoping semantics.

Nutshell
========

::

    <%inherit file="base.html"/>
    <%
        rows = [[v for v in range(0,10)] for row in range(0,10)]
    %>
    <table>
        % for row in rows:
            ${makerow(row)}
        % endfor
    </table>

    <%def name="makerow(row)">
        <tr>
        % for name in row:
            <td>${name}</td>\
        % endfor
        </tr>
    </%def>

Philosophy
===========

Python is a great scripting language. Don't reinvent the wheel...your templates can handle it !

Documentation
==============

See documentation for <PERSON><PERSON> at https://docs.makotemplates.org/en/latest/

License
========

Mako is licensed under an MIT-style license (see LICENSE).
Other incorporated projects may be licensed under different licenses.
All licenses allow for non-commercial and commercial use.
