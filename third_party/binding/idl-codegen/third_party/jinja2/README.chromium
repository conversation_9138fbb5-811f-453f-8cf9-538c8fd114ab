Name: Jinja2 Python Template Engine
Short Name: jinja2
URL: http://jinja.pocoo.org/
Version: 2.10
CPEPrefix: cpe:/a:pocoo:jinja2:2.10
License: BSD 3-Clause
License File: LICENSE
Security Critical: no

Description:
Template engine for code generation in Blink.

Source: https://pypi.python.org/packages/56/e6/332789f295cf22308386cf5bbd1f4e00ed11484299c5d7383378cf48ba47/Jinja2-2.10.tar.gz
MD5: 61ef1117f945486472850819b8d1eb3d
SHA-1: 34b69e5caab12ee37b9df69df9018776c008b7b8

Local Modifications:
This only includes the jinja2 directory from the tarball and the LICENSE and
AUTHORS files. Unit tests (testsuite directory) have been removed.
Additional chromium-specific files are:
* README.chromium (this file)
* OWNERS
* get_jinja2.sh (install script)
* jinja2.gni (generated by get_jinja2.sh)
* files of hashes (MD5 is also posted on website, SHA-512 computed locally).
Script checks hash then unpacks archive and installs desired files.
Retrieve or update by executing jinja2/get_jinja2.sh from third_party.
