{"ANGLEInstancedArrays": {"Attributes": [{"ExtAttributes": [], "Name": "timing", "Readonly": true, "Static": false, "Type": "AnimationEffectTiming"}, {"ExtAttributes": [{"Name": "maxC<PERSON><PERSON><PERSON><PERSON>nt"}], "Name": "computedTiming", "Readonly": true, "Static": false, "Type": "ComputedTimingProperties"}], "Consts": [{"ExtAttributes": [], "Name": "VERTEX_ATTRIB_ARRAY_DIVISOR", "Type": "unsigned long", "Value": "0x88FE"}], "ExtAttributes": [], "FilePath": "Source/modules/webgl/ANGLEInstancedArrays.idl", "Inherit": [], "Name": "ANGLEInstancedArrays", "Operations": [{"Arguments": [{"Name": "mode", "Type": "unsigned long"}], "ExtAttributes": [], "Name": "drawArraysInstancedANGLE", "Static": false, "Type": "void"}, {"Arguments": [{"Name": "mode", "Type": "unsigned long"}], "ExtAttributes": [], "Name": "drawElementsInstancedANGLE", "Static": false, "Type": "void"}]}, "AbstractWorker": {"Attributes": [{"ExtAttributes": [], "Name": "onerror", "Readonly": false, "Static": false, "Type": "EventHandler"}], "Consts": [], "ExtAttributes": [{"Name": "onerror"}], "FilePath": "Source/core/workers/AbstractWorker.idl", "Inherit": [], "Name": "AbstractWorker", "Operations": []}, "AnimationEvent": {"Attributes": [{"ExtAttributes": [], "Name": "animationName", "Readonly": true, "Static": false, "Type": "DOMString"}, {"ExtAttributes": [], "Name": "elapsedTime", "Readonly": true, "Static": false, "Type": "double"}], "Consts": [], "ExtAttributes": [{"Name": "animationName"}, {"Name": "elapsedTime"}], "FilePath": "Source/core/events/AnimationEvent.idl", "Inherit": [{"Name": "Event"}], "Name": "AnimationEvent", "Operations": []}}