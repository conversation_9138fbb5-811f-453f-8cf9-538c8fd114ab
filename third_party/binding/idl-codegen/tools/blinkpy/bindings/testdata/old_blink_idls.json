{"ANGLEInstancedArrays": {"Attributes": [{"ExtAttributes": [], "Name": "timing", "Readonly": true, "Static": false, "Type": "AnimationEffectTiming"}, {"ExtAttributes": [], "Name": "animVal", "Readonly": true, "Static": false, "Type": "SVGAngle"}], "Consts": [{"ExtAttributes": [], "Name": "VERTEX_ATTRIB_ARRAY_DIVISOR", "Type": "unsigned long", "Value": "0x88FE"}], "ExtAttributes": [], "FilePath": "Source/modules/webgl/ANGLEInstancedArrays.idl", "Inherit": [], "Name": "ANGLEInstancedArrays", "Operations": [{"Arguments": [{"Name": "mode", "Type": "unsigned long"}], "ExtAttributes": [], "Name": "drawArraysInstancedANGLE", "Static": false, "Type": "void"}, {"Arguments": [{"Name": "primcount", "Type": "long"}], "ExtAttributes": [], "Name": "drawElementsInstancedANGLE", "Static": false, "Type": "void"}]}, "AbstractWorker": {"Attributes": [{"ExtAttributes": [], "Name": "onerror", "Readonly": false, "Static": false, "Type": "EventHandler"}], "Consts": [], "ExtAttributes": [{"Name": "onerror"}], "FilePath": "Source/core/workers/AbstractWorker.idl", "Inherit": [], "Name": "AbstractWorker", "Operations": []}, "AnimationEffectReadOnly": {"Attributes": [{"ExtAttributes": [], "Name": "timing", "Readonly": true, "Static": false, "Type": "AnimationEffectTiming"}, {"ExtAttributes": [], "Name": "computedTiming", "Readonly": true, "Static": false, "Type": "ComputedTimingProperties"}], "Consts": [], "ExtAttributes": [{"Name": "timing"}, {"Name": "computedTiming"}], "FilePath": "Source/core/animation/AnimationEffectReadOnly.idl", "Inherit": [], "Name": "AnimationEffectReadOnly", "Operations": []}}