// Copyright 2024 The Lynx Authors. All rights reserved.
// Licensed under the Apache License Version 2.0 that can be found in the
// LICENSE file in the root directory of this source tree.
#undef napi_addon_register_func
#undef napi_module
#undef napi_add_env_cleanup_hook
#undef napi_remove_env_cleanup_hook
#undef napi_async_execute_callback
#undef napi_async_complete_callback
#undef napi_env
#undef napi_env__
#undef napi_value
#undef napi_value__
#undef napi_ref
#undef napi_ref__
#undef napi_handle_scope
#undef napi_handle_scope__
#undef napi_escapable_handle_scope
#undef napi_escapable_handle_scope__
#undef napi_callback_info
#undef napi_callback_info__
#undef napi_deferred
#undef napi_deferred__
#undef napi_default
#undef napi_writable
#undef napi_enumerable
#undef napi_configurable
#undef napi_static
#undef napi_default_method
#undef napi_default_jsproperty
#undef napi_property_attributes
#undef napi_undefined
#undef napi_null
#undef napi_boolean
#undef napi_number
#undef napi_string
#undef napi_symbol
#undef napi_object
#undef napi_function
#undef napi_external
#undef napi_bigint
#undef napi_valuetype
#undef napi_int8_array
#undef napi_uint8_array
#undef napi_uint8_clamped_array
#undef napi_int16_array
#undef napi_uint16_array
#undef napi_int32_array
#undef napi_uint32_array
#undef napi_float32_array
#undef napi_float64_array
#undef napi_bigint64_array
#undef napi_biguint64_array
#undef napi_typedarray_type
#undef napi_tsfn_nonblocking
#undef napi_tsfn_blocking
#undef napi_threadsafe_function_call_mode
#undef napi_threadsafe_function_call_js
#undef napi_ok
#undef napi_invalid_arg
#undef napi_object_expected
#undef napi_string_expected
#undef napi_name_expected
#undef napi_function_expected
#undef napi_number_expected
#undef napi_boolean_expected
#undef napi_array_expected
#undef napi_generic_failure
#undef napi_pending_exception
#undef napi_cancelled
#undef napi_escape_called_twice
#undef napi_handle_scope_mismatch
#undef napi_callback_scope_mismatch
#undef napi_queue_full
#undef napi_closing
#undef napi_bigint_expected
#undef napi_date_expected
#undef napi_arraybuffer_expected
#undef napi_detachable_arraybuffer_expected
#undef napi_conflict_instance_data
#undef napi_context_scope_mismatch
#undef napi_status
#undef napi_callback
#undef napi_finalize
#undef napi_property_descriptor
#undef napi_extended_error_info
#undef napi_get_last_error_info
